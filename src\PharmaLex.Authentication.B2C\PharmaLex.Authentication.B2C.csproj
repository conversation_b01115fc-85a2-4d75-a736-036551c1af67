﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AddRazorSupportForMvc>true</AddRazorSupportForMvc>
  </PropertyGroup>

	<PropertyGroup>
		<PackageId>PharmaLex.Authentication.B2C</PackageId>
		<PackageTags>PharmaLex;Authentication;Authentication.B2C;</PackageTags>
		<Authors>PharmaLex</Authors>
		<Company>PharmaLex Gmbh</Company>
		<Description>
			PharmaLex B2C Authentication package.
		</Description>
		<PackageIcon>logo.png</PackageIcon>
	</PropertyGroup>

	<PropertyGroup>
		<IncludeSymbols>true</IncludeSymbols>
		<SymbolPackageFormat>snupkg</SymbolPackageFormat>
	</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.12.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.4" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
    <PackageReference Include="Microsoft.Graph" Version="5.50.0" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.66.2" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.4.0" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.4.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.2.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Formats.Asn1" Version="8.0.1" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>
	
	<ItemGroup>
		<None Include="logo.png" Pack="true" PackagePath="\" />
	</ItemGroup>

</Project>
